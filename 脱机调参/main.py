# HSV模式触摸屏参数调整工具（全透明按钮版）
# 功能: 触摸屏调整HSV参数 | 实时二值化显示 | 优化UI设计 | 参数保存/加载/重置

from maix import image, camera, display, app, time, touchscreen
import cv2
import numpy as np
import threading
from collections import deque
import os

# ================ 可直接修改的全局参数 ================
# HSV参数初始值
INITIAL_H_MIN = 0
INITIAL_H_MAX = 30
INITIAL_S_MIN = 50
INITIAL_S_MAX = 255
INITIAL_V_MIN = 50
INITIAL_V_MAX = 255

# 屏幕布局参数
SCREEN_WIDTH = 640
SCREEN_HEIGHT = 480
CAMERA_WIDTH = 320
CAMERA_HEIGHT = 240

# UI布局参数 - 可根据需要调整
PARAM_PANEL_WIDTH = 280  # 参数面板总宽度
PARAM_HEIGHT = 70        # 每个参数行高度
BUTTON_WIDTH = 60        # 加减按钮宽度
BUTTON_HEIGHT = 40       # 加减按钮高度
BUTTON_MARGIN = 5        # 按钮与参数框边界的间距
VALUE_WIDTH = 60         # 值显示区域宽度

# 功能按钮参数
FUNCTION_BUTTON_WIDTH = 100   # 功能按钮宽度
FUNCTION_BUTTON_HEIGHT = 40   # 功能按钮高度
FUNCTION_BUTTON_MARGIN = 10   # 功能按钮间距

# 颜色配置 - 可根据需要调整（全透明设计）
BACKGROUND_COLOR = (0, 0, 0)       # 背景颜色
PARAM_BORDER_COLOR = (100, 100, 100)  # 参数框边框颜色（浅灰色，降低视觉干扰）
H_COLOR = (0, 0, 255)              # H参数颜色
S_COLOR = (0, 255, 0)              # S参数颜色
V_COLOR = (255, 0, 0)              # V参数颜色
TEXT_COLOR = (255, 255, 255)       # 文本颜色
FUNCTION_TEXT_COLOR = (255, 255, 255)  # 功能按钮文本颜色

# 交互参数
LONG_PRESS_THRESHOLD = 500  # 长按阈值(毫秒)
DEBOUNCE_THRESHOLD = 50     # 防抖阈值(毫秒)
INITIAL_ADJUST_INTERVAL = 100  # 初始调整间隔(毫秒)
MIN_ADJUST_INTERVAL = 30    # 最小调整间隔(毫秒)
ACCELERATION_FACTOR = 0.95  # 加速因子
MAX_ACCELERATED_STEP = 10   # 最大加速步长

# 文件路径配置
HSV_FILE_PATH = "/root/hsv.txt"  # HSV参数保存路径

# ====================================================

class TouchScreenReader:
    def __init__(self):
        self.ts = touchscreen.TouchScreen()
        self.running = False
        self.touch_thread = None
        self.touch_events = deque(maxlen=100)
        self.last_x = 0
        self.last_y = 0
        self.last_pressed = False
        print("[初始化] 触摸屏读取器已创建")
    
    def start(self):
        if not self.running:
            self.running = True
            self.touch_thread = threading.Thread(target=self._read_touch_loop)
            self.touch_thread.daemon = True
            self.touch_thread.start()
            print("[启动] 触摸屏读取线程已启动")
    
    def stop(self):
        if self.running:
            self.running = False
            if self.touch_thread:
                self.touch_thread.join(timeout=1.0)
            print("[停止] 触摸屏读取线程已停止")
    
    def _read_touch_loop(self):
        while self.running:
            try:
                x, y, pressed = self.ts.read()
                
                if pressed != self.last_pressed:
                    self.last_x = x
                    self.last_y = y
                    self.last_pressed = pressed
                    
                    if pressed:
                        self.touch_events.append(("press", x, y, time.ticks_ms()))
                    else:
                        self.touch_events.append(("release", self.last_x, self.last_y, time.ticks_ms()))
                    
                    print(f"[触摸事件] 类型: {'按下' if pressed else '释放'}, x={x}, y={y}")
                
                time.sleep(0.01)  # 保持最小延迟避免CPU占用
            
            except Exception as e:
                print(f"[错误] 读取触摸屏时发生异常: {e}")
                time.sleep(0.1)
    
    def get_events(self):
        events = list(self.touch_events)
        self.touch_events.clear()
        return events

class HSVController:
    def __init__(self):
        self.mode = "HSV"
        
        # HSV参数（使用全局初始值）
        self.h_min = INITIAL_H_MIN
        self.h_max = INITIAL_H_MAX
        self.s_min = INITIAL_S_MIN
        self.s_max = INITIAL_S_MAX
        self.v_min = INITIAL_V_MIN
        self.v_max = INITIAL_V_MAX
        
        # 屏幕和UI尺寸（使用全局参数）
        self.screen_width = SCREEN_WIDTH
        self.screen_height = SCREEN_HEIGHT
        self.camera_width = CAMERA_WIDTH
        self.camera_height = CAMERA_HEIGHT
        
        # 长按加速和防抖参数（使用全局参数）
        self.long_press_threshold = LONG_PRESS_THRESHOLD
        self.debounce_threshold = DEBOUNCE_THRESHOLD
        self.long_press_active = False
        self.long_press_start_time = 0
        self.long_press_action = None  # 'increase'或'decrease'
        
        # 长按加速参数（使用全局参数）
        self.initial_interval = INITIAL_ADJUST_INTERVAL
        self.min_interval = MIN_ADJUST_INTERVAL
        self.acceleration_factor = ACCELERATION_FACTOR
        self.current_interval = self.initial_interval
        self.last_adjust_time = 0
        self.long_press_duration = 0
        
        # 当前选中的参数和功能
        self.selected_param = None
        self.selected_step = 1
        self.function_action = None  # 用于标识功能按钮操作
        
        # 触摸状态
        self.touch_start_pos = None
        self.touch_end_pos = None
        self.last_touch_time = 0
        
        # 初始化触摸屏读取器
        self.touch_reader = TouchScreenReader()
        
        print("=== HSV参数调整工具（全透明按钮版）===")
        print(f"屏幕分辨率: {self.screen_width}x{self.screen_height}")
        print(f"摄像头分辨率: {self.camera_width}x{self.camera_height}")
        print("功能: 保存(SAVE)、加载(LOAD)、重置(RESET)")
        print("参数文件路径: ", HSV_FILE_PATH)
        print("所有按钮已设置为完全透明，仅保留文本标识")
        print("短按防抖和长按加速功能已启用")
        print("-------------------------------")
    
    def start_touch_detection(self):
        self.touch_reader.start()
    
    def stop_touch_detection(self):
        self.touch_reader.stop()
    
    def handle_touch_events(self):
        current_time = time.ticks_ms()
        events = self.touch_reader.get_events()
        
        for event in events:
            event_type, x, y, timestamp = event
            
            if event_type == "press":
                # 短按防抖处理
                if timestamp - self.last_touch_time < self.debounce_threshold:
                    print(f"[防抖] 忽略短时间内的重复按下事件: {timestamp - self.last_touch_time}ms")
                    continue
                
                self.last_touch_time = timestamp
                self._handle_press(x, y, timestamp)
            elif event_type == "release":
                self._handle_release(x, y, timestamp)
        
        # 处理长按逻辑（仅针对参数调整）
        if self.long_press_active and current_time - self.last_adjust_time > self.current_interval:
            self._perform_long_press_action()
            self.last_adjust_time = current_time
            self.long_press_duration = current_time - self.long_press_start_time
            
            # 实现长按加速
            if self.long_press_duration > 2000:  # 长按超过2秒开始加速
                self.current_interval = max(
                    int(self.current_interval * self.acceleration_factor),
                    self.min_interval
                )
                print(f"[加速] 长按持续时间: {self.long_press_duration}ms, 调整间隔: {self.current_interval}ms")
    
    def _handle_press(self, x, y, timestamp):
        self.touch_start_pos = (x, y)
        self.long_press_start_time = timestamp
        self.long_press_active = False
        self.long_press_action = None
        self.function_action = None
        self.current_interval = self.initial_interval  # 重置加速参数
        
        print(f"[调试] 触摸按下: x={x}, y={y}")
        
        # 先检查是否点击了功能按钮区域
        if self._check_function_button_press(x, y):
            return
            
        # 计算参数行索引
        param_index = y // PARAM_HEIGHT
        
        # 计算按钮区域
        value_area_start = PARAM_PANEL_WIDTH - VALUE_WIDTH - 2 * BUTTON_WIDTH - BUTTON_MARGIN
        inc_button_start = value_area_start + VALUE_WIDTH + BUTTON_MARGIN
        dec_button_start = inc_button_start + BUTTON_WIDTH
        
        # 检查是否点击了加号按钮
        if (inc_button_start <= x <= inc_button_start + BUTTON_WIDTH and 
            param_index * PARAM_HEIGHT + (PARAM_HEIGHT - BUTTON_HEIGHT) // 2 <= y <= param_index * PARAM_HEIGHT + (PARAM_HEIGHT + BUTTON_HEIGHT) // 2 and
            0 <= param_index < 6):
            param_names = ["h_min", "h_max", "s_min", "s_max", "v_min", "v_max"]
            self.selected_param = param_names[param_index]
            self.long_press_active = True
            self.long_press_action = "increase"
            print(f"[调试] 增加参数: {self.selected_param}")
        
        # 检查是否点击了减号按钮
        elif (dec_button_start <= x <= dec_button_start + BUTTON_WIDTH and 
              param_index * PARAM_HEIGHT + (PARAM_HEIGHT - BUTTON_HEIGHT) // 2 <= y <= param_index * PARAM_HEIGHT + (PARAM_HEIGHT + BUTTON_HEIGHT) // 2 and
              0 <= param_index < 6):
            param_names = ["h_min", "h_max", "s_min", "s_max", "v_min", "v_max"]
            self.selected_param = param_names[param_index]
            self.long_press_active = True
            self.long_press_action = "decrease"
            print(f"[调试] 减少参数: {self.selected_param}")
    
    def _check_function_button_press(self, x, y):
        """检查是否点击了功能按钮（保存、加载、重置）"""
        # 计算功能按钮位置（右下角区域）
        button_y = self.screen_height - FUNCTION_BUTTON_HEIGHT - FUNCTION_BUTTON_MARGIN
        
        # 保存按钮
        save_button_x = self.screen_width - 3 * FUNCTION_BUTTON_WIDTH - 2 * FUNCTION_BUTTON_MARGIN
        if (save_button_x <= x <= save_button_x + FUNCTION_BUTTON_WIDTH and
            button_y <= y <= button_y + FUNCTION_BUTTON_HEIGHT):
            self.function_action = "save"
            print("[调试] 点击保存按钮")
            return True
            
        # 加载按钮
        load_button_x = self.screen_width - 2 * FUNCTION_BUTTON_WIDTH - FUNCTION_BUTTON_MARGIN
        if (load_button_x <= x <= load_button_x + FUNCTION_BUTTON_WIDTH and
            button_y <= y <= button_y + FUNCTION_BUTTON_HEIGHT):
            self.function_action = "load"
            print("[调试] 点击加载按钮")
            return True
            
        # 重置按钮
        reset_button_x = self.screen_width - FUNCTION_BUTTON_WIDTH - FUNCTION_BUTTON_MARGIN
        if (reset_button_x <= x <= reset_button_x + FUNCTION_BUTTON_WIDTH and
            button_y <= y <= button_y + FUNCTION_BUTTON_HEIGHT):
            self.function_action = "reset"
            print("[调试] 点击重置按钮")
            return True
            
        return False
    
    def _handle_release(self, x, y, timestamp):
        press_duration = timestamp - self.long_press_start_time
        self.touch_end_pos = (x, y)
        
        # 处理功能按钮操作（保存/加载/重置）
        if self.function_action:
            dx = abs(x - self.touch_start_pos[0])
            dy = abs(y - self.touch_start_pos[1])
            
            # 如果移动距离小于阈值，认为是有效点击
            if dx < 15 and dy < 15:
                if self.function_action == "save":
                    self._save_parameters()
                elif self.function_action == "load":
                    self._load_parameters()
                elif self.function_action == "reset":
                    self._reset_parameters()
        
        # 短按处理（参数调整）
        elif press_duration < self.long_press_threshold and self.selected_param and self.long_press_action:
            dx = abs(x - self.touch_start_pos[0])
            dy = abs(y - self.touch_start_pos[1])
            
            # 如果移动距离小于阈值，认为是点击
            if dx < 15 and dy < 15:
                # 处理短按事件
                if self.long_press_action == "increase":
                    self._increase_param(self.selected_param, self.selected_step)
                    print(f"[短按] 增加参数: {self.selected_param}, 当前值: {getattr(self, self.selected_param)}")
                elif self.long_press_action == "decrease":
                    self._decrease_param(self.selected_param, self.selected_step)
                    print(f"[短按] 减少参数: {self.selected_param}, 当前值: {getattr(self, self.selected_param)}")
        
        self.long_press_active = False
        self.long_press_action = None
        self.function_action = None
        self.touch_start_pos = None
    
    def _perform_long_press_action(self):
        """执行长按操作（参数连续调整）"""
        if not self.selected_param or not self.long_press_action:
            return
        
        # 根据长按时间增加步长，实现加速效果
        if self.long_press_duration > 2000:  # 长按超过2秒开始加速
            # 动态计算步长，最大为MAX_ACCELERATED_STEP
            accelerated_step = min(
                int(self.selected_step * (1 + (self.long_press_duration - 2000) / 2000)),
                MAX_ACCELERATED_STEP
            )
        else:
            accelerated_step = self.selected_step
        
        if self.long_press_action == "increase":
            self._increase_param(self.selected_param, accelerated_step)
            print(f"[长按加速] 增加参数: {self.selected_param}, 当前值: {getattr(self, self.selected_param)}, 步长: {accelerated_step}")
        elif self.long_press_action == "decrease":
            self._decrease_param(self.selected_param, accelerated_step)
            print(f"[长按加速] 减少参数: {self.selected_param}, 当前值: {getattr(self, self.selected_param)}, 步长: {accelerated_step}")
    
    def _save_parameters(self):
        """保存当前HSV参数到文件"""
        try:
            with open(HSV_FILE_PATH, 'w') as f:
                f.write(f"h_min={self.h_min}\n")
                f.write(f"h_max={self.h_max}\n")
                f.write(f"s_min={self.s_min}\n")
                f.write(f"s_max={self.s_max}\n")
                f.write(f"v_min={self.v_min}\n")
                f.write(f"v_max={self.v_max}\n")
            
            print(f"[成功] 参数已保存到 {HSV_FILE_PATH}")
            # 显示保存成功提示
            self.show_temporary_message("SAVED", 1000)
        except Exception as e:
            print(f"[错误] 保存参数失败: {e}")
            self.show_temporary_message("SAVE FAILED", 1000)
    
    def _load_parameters(self):
        """从文件加载HSV参数"""
        try:
            if not os.path.exists(HSV_FILE_PATH):
                print(f"[错误] 加载失败，文件不存在: {HSV_FILE_PATH}")
                self.show_temporary_message("LOAD FAILED", 1000)
                return
                
            with open(HSV_FILE_PATH, 'r') as f:
                lines = f.readlines()
                
                for line in lines:
                    line = line.strip()
                    if not line:
                        continue
                        
                    key, value = line.split('=')
                    if key == 'h_min':
                        self.h_min = int(value)
                    elif key == 'h_max':
                        self.h_max = int(value)
                    elif key == 's_min':
                        self.s_min = int(value)
                    elif key == 's_max':
                        self.s_max = int(value)
                    elif key == 'v_min':
                        self.v_min = int(value)
                    elif key == 'v_max':
                        self.v_max = int(value)
            
            print(f"[成功] 已从 {HSV_FILE_PATH} 加载参数")
            self.show_temporary_message("LOADED", 1000)
        except Exception as e:
            print(f"[错误] 加载参数失败: {e}")
            self.show_temporary_message("LOAD FAILED", 1000)
    
    def _reset_parameters(self):
        """重置HSV参数（最小值为0，最大值为255）"""
        self.h_min = 0
        self.h_max = 180
        self.s_min = 0
        self.s_max = 255
        self.v_min = 0
        self.v_max = 255
        print("[成功] 参数已重置（最小值=0，最大值=255）")
        self.show_temporary_message("RESET", 1000)
    
    def show_temporary_message(self, text, duration):
        """显示临时消息（用于操作反馈）"""
        self.temp_message = text
        self.temp_message_end = time.ticks_ms() + duration
    
    def _increase_param(self, param_name, step=1):
        max_values = {
            "h_min": 178, "h_max": 179,
            "s_min": 254, "s_max": 255,
            "v_min": 254, "v_max": 255
        }
        
        current_value = getattr(self, param_name)
        max_value = max_values.get(param_name, 255)
        
        if current_value < max_value:
            new_value = min(current_value + step, max_value)
            if "min" in param_name:
                max_param = param_name.replace("min", "max")
                if getattr(self, max_param) < new_value:
                    setattr(self, max_param, new_value)
            setattr(self, param_name, new_value)
    
    def _decrease_param(self, param_name, step=1):
        min_values = {
            "h_min": 0, "h_max": 1,
            "s_min": 0, "s_max": 1,
            "v_min": 0, "v_max": 1
        }
        
        current_value = getattr(self, param_name)
        min_value = min_values.get(param_name, 0)
        
        if current_value > min_value:
            new_value = max(current_value - step, min_value)
            if "max" in param_name:
                min_param = param_name.replace("max", "min")
                if getattr(self, min_param) > new_value:
                    setattr(self, min_param, new_value)
            setattr(self, param_name, new_value)
    
    def process_image(self, img_cv):
        hsv = cv2.cvtColor(img_cv, cv2.COLOR_RGB2HSV)
        lower = np.array([self.h_min, self.s_min, self.v_min])
        upper = np.array([self.h_max, self.s_max, self.v_max])
        mask = cv2.inRange(hsv, lower, upper)
        return mask
    
    def draw_interface(self, img):
        # 缩放图像到屏幕尺寸
        img = img.resize(self.screen_width, self.screen_height)
        
        # 绘制标题
        self._draw_string(img,
            self.screen_width // 2 - 120, 20,
            "HSV PARAM ADJUSTER",
            color=TEXT_COLOR, scale=1.5
        )
        
        # 绘制当前步长
        self._draw_string(img,
            self.screen_width // 2 - 40, self.screen_height - 20 - FUNCTION_BUTTON_HEIGHT - FUNCTION_BUTTON_MARGIN,
            f"STEP: {self.selected_step}",
            color=TEXT_COLOR, scale=1.2
        )
        
        # 绘制长按状态指示器
        if self.long_press_active and self.selected_param:
            press_time = (time.ticks_ms() - self.long_press_start_time) / 1000
            self._draw_string(img,
                self.screen_width // 2 - 50, 50,
                f"HOLD: {press_time:.1f}s",
                color=(255, 255, 0), scale=1.2
            )
            
            # 显示加速状态
            if press_time > 2:
                self._draw_string(img,
                    self.screen_width // 2 - 50, 75,
                    f"ACCEL",
                    color=(255, 0, 0), scale=1.2
            )
        
        # 绘制参数框和按钮
        param_names = ["h_min", "h_max", "s_min", "s_max", "v_min", "v_max"]
        param_labels = ["H_M", "H_X", "S_M", "S_X", "V_M", "V_X"]  # 使用简短英文缩写
        
        for i, param in enumerate(param_names):
            if param.startswith("h_"):
                color = H_COLOR
            elif param.startswith("s_"):
                color = S_COLOR
            else:
                color = V_COLOR
            
            # 计算位置
            y_pos = i * PARAM_HEIGHT
            
            # 绘制参数背景（半透明黑色，提高文字可读性）
            img.draw_rect(
                0, y_pos, PARAM_PANEL_WIDTH, PARAM_HEIGHT,
                image.Color.from_rgb(0, 0, 0),  # 半透明黑色
                thickness=-1  # 填充
            )
            
            # 绘制参数框（浅色边框，降低视觉干扰）
            img.draw_rect(
                0, y_pos, PARAM_PANEL_WIDTH, PARAM_HEIGHT,
                image.Color.from_rgb(*PARAM_BORDER_COLOR),
                thickness=1
            )
            
            # 绘制参数名称（使用英文缩写）
            self._draw_string(img,
                20, y_pos + PARAM_HEIGHT // 2 - 12,
                param_labels[i],
                color=color, scale=1.8
            )
            
            # 计算值显示区域
            value_area_start = PARAM_PANEL_WIDTH - VALUE_WIDTH - 2 * BUTTON_WIDTH - BUTTON_MARGIN
            
            # 绘制值显示区域（完全透明，无背景和边框）
            # 仅保留参数值文本
            
            # 绘制参数值（居中显示）
            value = getattr(self, param)
            self._draw_string(img,
                value_area_start + VALUE_WIDTH // 2 - 20, y_pos + PARAM_HEIGHT // 2 - 12,
                f"{value:03d}",  # 固定显示3位数字
                color=color, scale=1.8
            )
            
            # 计算按钮位置（完全透明，仅保留+/-文本）
            inc_button_x = value_area_start + VALUE_WIDTH + BUTTON_MARGIN
            dec_button_x = inc_button_x + BUTTON_WIDTH
            button_y = y_pos + (PARAM_HEIGHT - BUTTON_HEIGHT) // 2
            
            # 绘制加号按钮（完全透明，仅显示+号文本）
            self._draw_string(img,
                inc_button_x + BUTTON_WIDTH // 2 - 8, button_y + BUTTON_HEIGHT // 2 - 15,
                "+",
                color=TEXT_COLOR, scale=2.0
            )
            
            # 绘制减号按钮（完全透明，仅显示-号文本）
            self._draw_string(img,
                dec_button_x + BUTTON_WIDTH // 2 - 8, button_y + BUTTON_HEIGHT // 2 - 15,
                "-",
                color=TEXT_COLOR, scale=2.0
            )
        
        # 绘制功能按钮（完全透明，仅保留文本标识）
        self._draw_function_buttons(img)
        
        # 显示当前调整的参数和值
        if self.selected_param:
            param_index = param_names.index(self.selected_param)
            param_label = param_labels[param_index]
            param_value = getattr(self, self.selected_param)
            self._draw_string(img,
                self.screen_width // 2 - 100, self.screen_height - 50 - FUNCTION_BUTTON_HEIGHT - FUNCTION_BUTTON_MARGIN,
                f"ADJUSTING: {param_label} = {param_value}",
                color=(255, 255, 0), scale=1.2
            )
        
        # 显示临时消息（如保存/加载/重置结果）
        if hasattr(self, 'temp_message') and time.ticks_ms() < self.temp_message_end:
            self._draw_string(img,
                self.screen_width // 2 - 60, self.screen_height // 2,
                self.temp_message,
                color=(0, 255, 0) if "SUCCESS" in self.temp_message or "SAVED" in self.temp_message or "LOADED" in self.temp_message or "RESET" in self.temp_message else (255, 0, 0),
                scale=2.0
            )
        elif hasattr(self, 'temp_message'):
            delattr(self, 'temp_message')
        
        return img
    
    def _draw_function_buttons(self, img):
        """绘制功能按钮（完全透明，仅保留文本）"""
        button_y = self.screen_height - FUNCTION_BUTTON_HEIGHT - FUNCTION_BUTTON_MARGIN
        
        # 保存按钮（完全透明，仅显示SAVE文本）
        save_button_x = self.screen_width - 3 * FUNCTION_BUTTON_WIDTH - 2 * FUNCTION_BUTTON_MARGIN
        self._draw_string(img,
            save_button_x + 10, button_y + FUNCTION_BUTTON_HEIGHT // 2 - 10,
            "SAVE",
            color=FUNCTION_TEXT_COLOR, scale=1.2
        )
        
        # 加载按钮（完全透明，仅显示LOAD文本）
        load_button_x = self.screen_width - 2 * FUNCTION_BUTTON_WIDTH - FUNCTION_BUTTON_MARGIN
        self._draw_string(img,
            load_button_x + 10, button_y + FUNCTION_BUTTON_HEIGHT // 2 - 10,
            "LOAD",
            color=FUNCTION_TEXT_COLOR, scale=1.2
        )
        
        # 重置按钮（完全透明，仅显示RESET文本）
        reset_button_x = self.screen_width - FUNCTION_BUTTON_WIDTH - FUNCTION_BUTTON_MARGIN
        self._draw_string(img,
            reset_button_x + 5, button_y + FUNCTION_BUTTON_HEIGHT // 2 - 10,
            "RESET",
            color=FUNCTION_TEXT_COLOR, scale=1.2
        )
    
    def _draw_string(self, img, x, y, text, color=(255, 255, 255), scale=1.0):
        """绘制文本"""
        img.draw_string(x, y, text, image.Color.from_rgb(*color), scale=scale)

def main():
    controller = HSVController()
    controller.start_touch_detection()
    
    try:
        cam = camera.Camera(320, 240)
        disp = display.Display()
        print(f"[调试] 相机和显示初始化成功")
    except Exception as e:
        print(f"[错误] 初始化相机或显示失败: {e}")
        controller.stop_touch_detection()
        return
    
    try:
        frame_count = 0
        last_fps_time = time.ticks_ms()
        
        while not app.need_exit():
            frame_count += 1
            
            current_time = time.ticks_ms()
            if current_time - last_fps_time >= 1000:
                fps = frame_count / ((current_time - last_fps_time) / 1000)
                print(f"[性能] FPS: {fps:.2f}")
                frame_count = 0
                last_fps_time = current_time
            
            controller.handle_touch_events()
            
            try:
                img = cam.read()
                img_cv = image.image2cv(img, ensure_bgr=False, copy=False)
                
                mask = controller.process_image(img_cv)
                mask_img = image.cv2image(mask, bgr=False, copy=False)
                
                display_img = controller.draw_interface(mask_img)
                
                disp.show(display_img)
            except Exception as e:
                print(f"[错误] 图像处理错误: {e}")
                try:
                    disp.show(img)
                except:
                    pass
            
    except Exception as e:
        print(f"[错误] 运行时错误: {e}")
    finally:
        controller.stop_touch_detection()
        try:
            cam.release()
            print("[调试] 相机资源已释放")
        except:
            pass

if __name__ == "__main__":
    main()